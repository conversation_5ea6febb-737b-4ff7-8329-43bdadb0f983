import type { ThemeConfig } from "../types/theme";
import { STORAGE_KEY, DEFAULT_CONFIG, THEME_MODES } from "./constants";

/**
 * Get stored theme configuration from localStorage
 */
export function getStoredTheme(storageKey: string = STORAGE_KEY): ThemeConfig {
  if (typeof window === "undefined") return DEFAULT_CONFIG;

  try {
    const stored = localStorage.getItem(storageKey);
    if (!stored) return DEFAULT_CONFIG;

    const parsed = JSON.parse(stored);
    return {
      mode: THEME_MODES.includes(parsed.mode)
        ? parsed.mode
        : DEFAULT_CONFIG.mode,
      variant:
        typeof parsed.variant === "string"
          ? parsed.variant
          : DEFAULT_CONFIG.variant,
    };
  } catch {
    return DEFAULT_CONFIG;
  }
}

/**
 * Persist theme configuration to localStorage
 */
export function persistTheme(
  config: ThemeConfig,
  storageKey: string = STORAGE_KEY
): void {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(storageKey, JSON.stringify(config));
  } catch {
    // Silently fail if localStorage is not available
  }
}
