// Main hook and provider
export { useTheme, useThemeOptional } from "./components/theme-provider";
export { ThemeProvider } from "./components/theme-provider";
export type { ThemeProviderProps } from "./components/theme-provider";

// Components
export { Button } from "./components/button";

// Theme components
export { ThemeToggle, ThemeVariantSelector } from "./components/theme-toggle";

// Types
export type {
  ThemeMode,
  ThemeVariant,
  ThemeConfig,
  UseThemeReturn,
} from "./types/theme";

// Utils (for advanced usage)
export { applyTheme, getSystemTheme, isDarkMode } from "./utils/theme";
export { getStoredTheme, persistTheme } from "./lib/storage";
export { THEME_MODES, DEFAULT_CONFIG } from "./lib/constants";
