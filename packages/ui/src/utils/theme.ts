import type { ThemeConfig } from "../types/theme";

/**
 * Get system color scheme preference
 */
export function getSystemTheme(): "light" | "dark" {
  if (typeof window === "undefined") return "light";
  return window.matchMedia("(prefers-color-scheme: dark)").matches
    ? "dark"
    : "light";
}

/**
 * Determine if current config should use dark mode
 */
export function isDarkMode(config: ThemeConfig): boolean {
  return config.mode === "auto"
    ? getSystemTheme() === "dark"
    : config.mode === "dark";
}

/**
 * Dynamically detect available theme variants from CSS
 */
export function getAvailableVariants(): string[] {
  if (typeof document === "undefined") return ["default"];

  const variants = new Set<string>(["default"]);

  // Check all stylesheets for theme variant classes
  try {
    for (const stylesheet of document.styleSheets) {
      try {
        for (const rule of stylesheet.cssRules || []) {
          if (rule instanceof CSSStyleRule) {
            const selector = rule.selectorText;
            // Match .theme-{variant} patterns
            const match = selector.match(/\.theme-([a-zA-Z0-9-_]+)(?:\s|$|\.)/);
            if (match) {
              variants.add(match[1]);
            }
          }
        }
      } catch {
        // Skip stylesheets that can't be accessed (CORS)
        continue;
      }
    }
  } catch (e) {
    // Fallback if stylesheet access fails
    console.warn("Could not detect theme variants from CSS:", e);
  }

  return Array.from(variants).sort();
}

/**
 * Apply theme classes to document element
 */
export function applyTheme(config: ThemeConfig): void {
  if (typeof document === "undefined") return;

  const { classList } = document.documentElement;

  // Remove existing theme classes
  classList.remove("light", "dark");
  Array.from(classList)
    .filter((cls) => cls.startsWith("theme-"))
    .forEach((cls) => classList.remove(cls));

  // Apply new theme classes
  const resolvedMode = config.mode === "auto" ? getSystemTheme() : config.mode;
  classList.add(resolvedMode);

  if (config.variant !== "default") {
    classList.add(`theme-${config.variant}`);
  }
}
