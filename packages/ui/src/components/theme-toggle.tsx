import { Button } from "./button";
import { useTheme } from "./theme-provider";

export function ThemeToggle({ showLabels = false }: { showLabels?: boolean }) {
  const { mode, toggleMode } = useTheme();

  return (
    <Button onClick={toggleMode} variant="outline" size="sm">
      {showLabels ? `Mode: ${mode}` : "🌓"}
    </Button>
  );
}

export function ThemeVariantSelector({
  variants = ["default", "gray", "slate", "blue"],
  className = "",
}: {
  variants?: string[];
  className?: string;
}) {
  const { variant, setVariant } = useTheme();

  return (
    <div className={`flex gap-2 flex-wrap ${className}`}>
      {variants.map((v) => (
        <Button
          key={v}
          onClick={() => setVariant(v)}
          variant={variant === v ? "default" : "outline"}
          size="sm"
        >
          {v.charAt(0).toUpperCase() + v.slice(1)}
        </Button>
      ))}
    </div>
  );
}
