import { Button } from "./button";
import { useTheme } from "./theme-provider";

export function ThemeToggle({
  modeOnly = false,
  variantOnly = false,
  showLabels = false,
}: {
  modeOnly?: boolean;
  variantOnly?: boolean;
  showLabels?: boolean;
}) {
  const { mode, variant, toggleMode, cycleVariant } = useTheme();

  if (modeOnly) {
    return (
      <Button onClick={toggleMode} variant="outline" size="sm">
        {showLabels ? `Mode: ${mode}` : "🌓"}
      </Button>
    );
  }

  if (variantOnly) {
    return (
      <Button onClick={cycleVariant} variant="outline" size="sm">
        {showLabels ? `Variant: ${variant}` : "🎨"}
      </Button>
    );
  }

  return (
    <div className="flex gap-2">
      <Button onClick={toggleMode} variant="outline" size="sm">
        {showLabels ? `Mode: ${mode}` : "🌓"}
      </Button>
      <Button onClick={cycleVariant} variant="outline" size="sm">
        {showLabels ? `Variant: ${variant}` : "🎨"}
      </Button>
    </div>
  );
}

export function ThemeVariantSelector({
  dropdown = false,
  className = "",
}: {
  dropdown?: boolean;
  className?: string;
}) {
  const { variant, setVariant, availableVariants } = useTheme();

  if (dropdown) {
    return (
      <select
        value={variant}
        onChange={(e) => setVariant(e.target.value)}
        className={`px-3 py-2 border rounded-md bg-background ${className}`}
      >
        {availableVariants.map((v) => (
          <option key={v} value={v}>
            {v.charAt(0).toUpperCase() + v.slice(1)}
          </option>
        ))}
      </select>
    );
  }

  return (
    <div className={`flex gap-2 flex-wrap ${className}`}>
      {availableVariants.map((v) => (
        <Button
          key={v}
          onClick={() => setVariant(v)}
          variant={variant === v ? "default" : "outline"}
          size="sm"
        >
          {v.charAt(0).toUpperCase() + v.slice(1)}
        </Button>
      ))}
    </div>
  );
}
