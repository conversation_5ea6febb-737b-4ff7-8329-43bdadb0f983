import { Button } from "./button";
import { useTheme } from "./theme-provider";

export interface ThemeToggleProps {
  showLabels?: boolean;
  modeOnly?: boolean;
  variantOnly?: boolean;
}

export function ThemeToggle({
  showLabels = false,
  modeOnly = false,
  variantOnly = false,
}: ThemeToggleProps) {
  const { mode, variant, toggleMode, cycleVariant } = useTheme();

  if (variantOnly) {
    return (
      <Button onClick={cycleVariant} variant="outline" size="sm">
        {showLabels ? `Variant: ${variant}` : "🎨"}
      </Button>
    );
  }

  if (modeOnly) {
    return (
      <Button onClick={toggleMode} variant="outline" size="sm">
        {showLabels ? `Mode: ${mode}` : "🌓"}
      </Button>
    );
  }

  // Combined toggle (cycles through modes first, then variants)
  return (
    <Button onClick={toggleMode} variant="outline" size="sm">
      {showLabels ? `${mode} / ${variant}` : "🌓"}
    </Button>
  );
}

export function ThemeVariantSelector({
  variants,
  className = "",
}: {
  variants?: string[];
  className?: string;
}) {
  const { variant, setVariant, availableVariants } = useTheme();
  const variantsToShow = variants || availableVariants;

  return (
    <div className={`flex gap-2 flex-wrap ${className}`}>
      {variantsToShow.map((v) => (
        <Button
          key={v}
          onClick={() => setVariant(v)}
          variant={variant === v ? "default" : "outline"}
          size="sm"
        >
          {v.charAt(0).toUpperCase() + v.slice(1)}
        </Button>
      ))}
    </div>
  );
}
