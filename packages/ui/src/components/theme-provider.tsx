import React, { createContext, useContext, useEffect, useState } from "react";
import type {
  ThemeConfig,
  ThemeMode,
  ThemeVariant,
  UseThemeReturn,
} from "../types/theme";
import { THEME_MODES, DEFAULT_CONFIG } from "../lib/constants";
import { getStoredTheme, persistTheme } from "../lib/storage";
import { isDarkMode, applyTheme } from "../utils/theme";

// Theme context
const ThemeContext = createContext<UseThemeReturn | undefined>(undefined);

// Theme provider props
export interface ThemeProviderProps {
  children: React.ReactNode;
  /** Initial theme configuration (overrides localStorage) */
  defaultTheme?: Partial<ThemeConfig>;
  /** Available theme variants for this app */
  availableVariants?: string[];
  /** Custom storage key (defaults to 'theme') */
  storageKey?: string;
  /** Disable localStorage persistence */
  disableStorage?: boolean;
  /** CSS attribute to apply theme to (defaults to 'class') */
  attribute?: "class" | "data-theme";
  /** Force a specific theme (disables user selection) */
  forcedTheme?: ThemeConfig;
}

/**
 * Theme provider component that manages theme state and provides context
 */
export function ThemeProvider({
  children,
  defaultTheme,
  availableVariants = ["default"],
  storageKey = "theme",
  disableStorage = false,
  attribute = "class",
  forcedTheme,
}: ThemeProviderProps) {
  // Initialize theme config
  const [config, setConfig] = useState<ThemeConfig>(() => {
    if (forcedTheme) return forcedTheme;

    const stored = disableStorage ? DEFAULT_CONFIG : getStoredTheme(storageKey);
    return {
      ...DEFAULT_CONFIG,
      ...defaultTheme,
      ...stored,
    };
  });

  // Apply theme to DOM whenever config changes
  useEffect(() => {
    const themeToApply = forcedTheme || config;

    if (attribute === "class") {
      applyTheme(themeToApply);
    } else {
      // Apply as data attribute
      if (typeof document !== "undefined") {
        const resolvedMode =
          themeToApply.mode === "auto"
            ? window.matchMedia("(prefers-color-scheme: dark)").matches
              ? "dark"
              : "light"
            : themeToApply.mode;

        document.documentElement.setAttribute(
          "data-theme",
          `${resolvedMode}-${themeToApply.variant}`
        );
      }
    }

    // Persist to storage if not disabled and not forced
    if (!disableStorage && !forcedTheme) {
      persistTheme(config, storageKey);
    }
  }, [config, attribute, disableStorage, forcedTheme]);

  // Listen for system theme changes when in auto mode
  useEffect(() => {
    const currentConfig = forcedTheme || config;
    if (currentConfig.mode !== "auto") return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleChange = () => {
      if (attribute === "class") {
        applyTheme(currentConfig);
      } else {
        const resolvedMode = mediaQuery.matches ? "dark" : "light";
        document.documentElement.setAttribute(
          "data-theme",
          `${resolvedMode}-${currentConfig.variant}`
        );
      }
    };

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [config, attribute, forcedTheme]);

  // Theme control functions
  const setMode = React.useCallback(
    (mode: ThemeMode) => {
      if (forcedTheme) return; // Ignore if theme is forced
      setConfig((prev) => ({ ...prev, mode }));
    },
    [forcedTheme]
  );

  const setVariant = React.useCallback(
    (variant: ThemeVariant) => {
      if (forcedTheme) return; // Ignore if theme is forced
      if (!availableVariants.includes(variant)) {
        console.warn(
          `Theme variant "${variant}" is not in availableVariants:`,
          availableVariants
        );
        return;
      }
      setConfig((prev) => ({ ...prev, variant }));
    },
    [availableVariants, forcedTheme]
  );

  const toggleMode = React.useCallback(() => {
    if (forcedTheme) return; // Ignore if theme is forced
    setConfig((prev) => {
      const currentIndex = THEME_MODES.indexOf(prev.mode);
      const nextMode = THEME_MODES[(currentIndex + 1) % THEME_MODES.length];
      return { ...prev, mode: nextMode };
    });
  }, [forcedTheme]);

  const cycleVariant = React.useCallback(() => {
    if (forcedTheme) return; // Ignore if theme is forced
    setConfig((prev) => {
      const currentIndex = availableVariants.indexOf(prev.variant);
      const nextIndex = (currentIndex + 1) % availableVariants.length;
      return { ...prev, variant: availableVariants[nextIndex] };
    });
  }, [availableVariants, forcedTheme]);

  // Current theme values (use forced theme if provided)
  const currentConfig = forcedTheme || config;

  const value: UseThemeReturn = {
    mode: currentConfig.mode,
    variant: currentConfig.variant,
    isDark: isDarkMode(currentConfig),
    availableVariants,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}

/**
 * Hook to access theme context
 */
export function useTheme(): UseThemeReturn {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }

  return context;
}

/**
 * Hook to check if component is within ThemeProvider
 */
export function useThemeOptional(): UseThemeReturn | undefined {
  return useContext(ThemeContext);
}
