import React, { createContext, useContext, useEffect, useState } from "react";
import type {
  ThemeConfig,
  ThemeMode,
  ThemeVariant,
  UseThemeReturn,
} from "../types/theme";
import { THEME_MODES, DEFAULT_CONFIG } from "../lib/constants";
import { getStoredTheme, persistTheme } from "../lib/storage";
import { isDarkMode, applyTheme, getAvailableVariants } from "../utils/theme";

// Theme context
const ThemeContext = createContext<UseThemeReturn | undefined>(undefined);

// Theme provider props
export interface ThemeProviderProps {
  children: React.ReactNode;
  /** Initial theme configuration (overrides localStorage) */
  defaultTheme?: Partial<ThemeConfig>;
  /** Custom storage key (defaults to 'theme') */
  storageKey?: string;
  /** Disable localStorage persistence */
  disableStorage?: boolean;
}

/**
 * Theme provider component that manages theme state and provides context
 */
export function ThemeProvider({
  children,
  defaultTheme,
  storageKey = "theme",
  disableStorage = false,
}: ThemeProviderProps) {
  // Initialize theme config
  const [config, setConfig] = useState<ThemeConfig>(() => {
    const stored = disableStorage ? DEFAULT_CONFIG : getStoredTheme(storageKey);
    return {
      ...DEFAULT_CONFIG,
      ...defaultTheme,
      ...stored,
    };
  });

  // Apply theme to DOM whenever config changes
  useEffect(() => {
    applyTheme(config);

    // Persist to storage if not disabled
    if (!disableStorage) {
      persistTheme(config, storageKey);
    }
  }, [config, disableStorage, storageKey]);

  // Listen for system theme changes when in auto mode
  useEffect(() => {
    if (config.mode !== "auto") return;

    const mediaQuery = window.matchMedia("(prefers-color-scheme: dark)");
    const handleChange = () => applyTheme(config);

    mediaQuery.addEventListener("change", handleChange);
    return () => mediaQuery.removeEventListener("change", handleChange);
  }, [config]);

  // Theme control functions
  const setMode = React.useCallback((mode: ThemeMode) => {
    setConfig((prev) => ({ ...prev, mode }));
  }, []);

  const setVariant = React.useCallback((variant: ThemeVariant) => {
    setConfig((prev) => ({ ...prev, variant }));
  }, []);

  const toggleMode = React.useCallback(() => {
    setConfig((prev) => {
      const currentIndex = THEME_MODES.indexOf(prev.mode);
      const nextMode = THEME_MODES[(currentIndex + 1) % THEME_MODES.length];
      return { ...prev, mode: nextMode };
    });
  }, []);

  // Dynamically get available variants
  const availableVariants = React.useMemo(() => {
    return getAvailableVariants();
  }, []);

  const cycleVariant = React.useCallback(() => {
    setConfig((prev) => {
      const currentIndex = availableVariants.indexOf(prev.variant);
      const nextIndex = (currentIndex + 1) % availableVariants.length;
      return { ...prev, variant: availableVariants[nextIndex] };
    });
  }, [availableVariants]);

  // Current theme values
  const currentConfig = config;

  const value: UseThemeReturn = {
    mode: currentConfig.mode,
    variant: currentConfig.variant,
    isDark: isDarkMode(currentConfig),
    availableVariants,
    setMode,
    setVariant,
    toggleMode,
    cycleVariant,
  };

  return (
    <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
  );
}

/**
 * Hook to access theme context
 */
export function useTheme(): UseThemeReturn {
  const context = useContext(ThemeContext);

  if (context === undefined) {
    throw new Error("useTheme must be used within a ThemeProvider");
  }

  return context;
}

/**
 * Hook to check if component is within ThemeProvider
 */
export function useThemeOptional(): UseThemeReturn | undefined {
  return useContext(ThemeContext);
}
